"""
Redis服务模块
提供Redis连接和发布订阅功能
"""
import asyncio
from typing import Optional, Callable, Dict, Any, List, Union, Tuple
from utils.log_utils import get_logger
import redis.asyncio as redis

logger = get_logger(__name__)

class RedisService:
    """Redis服务类，处理Redis连接和发布订阅功能"""
    
    _instance = None
    _redis_client = None
    _pubsub = None
    _subscriber_task = None
    _channel_handlers = {}
    
    @classmethod
    async def initialize(cls, 
                        host: str = 'localhost', 
                        port: int = 6379, 
                        db: int = 0, 
                        password: Optional[str] = None,
                        sentinel_nodes: Optional[List[Union[str, Tuple[str, int]]]] = None,
                        sentinel_master: Optional[str] = None) -> None:
        """
        初始化Redis连接
        
        Args:
            host: Redis主机地址 (单机模式)
            port: Redis端口 (单机模式)
            db: Redis数据库编号
            password: Redis密码
            sentinel_nodes: Sentinel节点列表，格式为 [("host1", port1), ("host2", port2), ...] 或 ["host1:port1", "host2:port2", ...]
            sentinel_master: Sentinel主节点名称
        """
        if cls._redis_client is None:
            try:
                # 判断是使用Sentinel模式还是单机模式
                if sentinel_nodes and sentinel_master:
                    logger.info(f"使用Sentinel模式连接Redis，master: {sentinel_master}, nodes: {sentinel_nodes}")
                    
                    # 处理sentinel_nodes格式
                    formatted_nodes = []
                    for node in sentinel_nodes:
                        if isinstance(node, str) and ":" in node:
                            host, port = node.split(":")
                            formatted_nodes.append((host, int(port)))
                        elif isinstance(node, tuple) and len(node) == 2:
                            formatted_nodes.append(node)
                        else:
                            logger.warning(f"忽略无效的Sentinel节点格式: {node}")
                    
                    # 创建Sentinel连接
                    sentinel = redis.Sentinel(
                        formatted_nodes,
                        password=password,
                        socket_timeout=1.0,
                        decode_responses=True
                    )
                    
                    # 获取主节点连接
                    cls._redis_client = sentinel.master_for(
                        sentinel_master,
                        db=db
                    )
                    logger.info(f"Redis Sentinel连接初始化成功，master: {sentinel_master}")
                else:
                    # 单机模式
                    logger.info(f"使用单机模式连接Redis: {host}:{port}/{db}")
                    cls._redis_client = redis.Redis(
                        host=host,
                        port=port,
                        db=db,
                        password=password,
                        decode_responses=True
                    )
                    logger.info(f"Redis连接初始化成功: {host}:{port}/{db}")
                
                # 创建PubSub对象
                cls._pubsub = cls._redis_client.pubsub()
                
                # 启动订阅任务
                cls._start_subscriber()
                
            except Exception as e:
                logger.error(f"Redis连接初始化失败: {e}", exc_info=True)
                raise
    
    @classmethod
    async def close(cls) -> None:
        """关闭Redis连接"""
        if cls._subscriber_task and not cls._subscriber_task.done():
            cls._subscriber_task.cancel()
            try:
                await cls._subscriber_task
            except asyncio.CancelledError:
                logger.info("Redis订阅任务已取消")
        
        if cls._pubsub:
            await cls._pubsub.close()
            logger.info("Redis PubSub已关闭")
            
        if cls._redis_client:
            await cls._redis_client.close()
            logger.info("Redis连接已关闭")
            cls._redis_client = None
    
    @classmethod
    def _start_subscriber(cls) -> None:
        """启动订阅任务"""
        if cls._subscriber_task is None or cls._subscriber_task.done():
            cls._subscriber_task = asyncio.create_task(cls._message_listener())
            logger.info("Redis订阅任务已启动")
    
    @classmethod
    async def _message_listener(cls) -> None:
        """消息监听器，处理订阅的消息"""
        try:
            async for message in cls._pubsub.listen():
                if message['type'] == 'message':
                    channel = message['channel']
                    data = message['data']
                    logger.info(f"收到消息: 频道={channel}, 数据={data}")
                    
                    # 调用对应频道的处理函数
                    if channel in cls._channel_handlers:
                        try:
                            await cls._channel_handlers[channel](data)
                        except Exception as e:
                            logger.error(f"处理消息时出错: {e}", exc_info=True)
        except asyncio.CancelledError:
            logger.info("Redis消息监听器已取消")
        except Exception as e:
            logger.error(f"Redis消息监听器出错: {e}", exc_info=True)
            # 尝试重新启动监听器
            await asyncio.sleep(1)
            cls._start_subscriber()
    
    @classmethod
    async def subscribe(cls, channel: str, handler: Callable[[str], Any]) -> None:
        """
        订阅频道
        
        Args:
            channel: 频道名称
            handler: 消息处理函数，接收消息数据作为参数
        """
        if cls._pubsub is None:
            raise RuntimeError("Redis PubSub未初始化")
        
        # 注册处理函数
        cls._channel_handlers[channel] = handler
        
        # 订阅频道
        await cls._pubsub.subscribe(channel)
        logger.info(f"已订阅频道: {channel}")
    
    @classmethod
    async def publish(cls, channel: str, message: str) -> None:
        """
        发布消息到频道
        
        Args:
            channel: 频道名称
            message: 消息内容
        """
        if cls._redis_client is None:
            raise RuntimeError("Redis客户端未初始化")
        
        # 发布消息
        await cls._redis_client.publish(channel, message)
        logger.info(f"已发布消息到频道: {channel}, 消息: {message}")
    
    @classmethod
    async def get_instance(cls) -> 'RedisService':
        """获取RedisService实例"""
        if cls._instance is None:
            cls._instance = RedisService()
        return cls._instance