"""
聊天服务模块
处理聊天相关的业务逻辑
"""
from typing import Optional, AsyncIterator, Dict, Literal
from uuid import uuid4
import asyncio
import json
from datetime import datetime
from fastapi.responses import StreamingResponse, JSONResponse
from langchain_core.messages import HumanMessage, SystemMessage

from core.agent.graph_builder import graph_builder
from core.db.checkpoint import get_aiomysql_saver
from utils.log_utils import get_logger
from utils.response_utils import serialise_ai_message_chunk, format_sse_event
from langgraph.types import Command
from core.agent.agent import stop_events, stop_events_timestamps
import time
from core.db.redis_service import RedisService


logger = get_logger(__name__)

class ChatService:
    """聊天服务类，处理聊天相关的业务逻辑"""
    
    @classmethod
    async def handle_stop_message(cls, message: str) -> None:
        """
        处理停止消息
        
        Args:
            message: 消息内容，格式为JSON字符串，包含thread_id
        """
        try:
            # 解析消息
            data = json.loads(message)
            thread_id = data.get("thread_id")
            
            if not thread_id:
                logger.warning("收到的停止消息缺少thread_id")
                return
            
            logger.info(f"收到停止消息，thread_id: {thread_id}")
            
            # 检查是否存在该thread_id的停止事件
            if thread_id in stop_events:
                # 设置停止事件
                stop_events[thread_id].set()
                logger.info(f"已设置停止事件，thread_id: {thread_id}")
        except json.JSONDecodeError:
            logger.error(f"解析停止消息时出错，消息格式不正确: {message}")
        except Exception as e:
            logger.error(f"处理停止消息时出错: {e}", exc_info=True)
    
    async def generate_chat_responses(self, message: Optional[str], user_id: int, brand_id: int, 
                                     session_id: Optional[str] = None, resume: int = 0, 
                                     resume_action: Optional[Literal["continue", "reject"]] = None, checkpoint_id: Optional[str] = None) -> AsyncIterator[str]:
        """
        生成响应
        
        Args:
            message: 用户消息，如果为None则表示从检查点恢复
            user_id: 用户ID
            brand_id: 品牌ID
            session_id: 会话ID，如果为None则创建新会话
            resume: 是否从中断中恢复，1表示从中断中恢复，0表示正常操作
            resume_action: 恢复操作的动作，可以是"continue"或"reject"
            checkpoint_id: 检查点ID，如果提供则从此检查点重新生成
        """
        try:
            # 确定thread_id
            thread_id = session_id if session_id else str(uuid4())
            
            # 初始化或重置停止事件
            if thread_id not in stop_events:
                stop_events[thread_id] = asyncio.Event()
                stop_events_timestamps[thread_id] = time.time()
            else:
                stop_events[thread_id].clear()
                stop_events_timestamps[thread_id] = time.time()  # 更新时间戳
                
            async with get_aiomysql_saver() as checkpointer:
                agent = graph_builder.compile(checkpointer=checkpointer, name="wangwei_agent")
                is_new_conversation = session_id is None
                title = None
                
                # 配置
                config = {
                    "configurable": {
                        "thread_id": thread_id,
                        "user_id": user_id,
                        "brand_id": brand_id
                    }
                }
                
                # 如果提供了checkpoint_id，添加到配置中
                if checkpoint_id:
                    config["configurable"]["checkpoint_id"] = checkpoint_id
                
                # 初始化输入数据
                input_data = None
                
                # 只有当message不为None时才设置input_data
                if message is not None:
                    if is_new_conversation:
                        new_session_id = config["configurable"]["thread_id"]
                        
                        # 初始化状态
                        input_data = {
                            "messages": [
                              HumanMessage(content=message)
                            ],
                            "title_generated": False,
                            "title": None
                        }
                        
                        # 使用事件类型代替JSON中的type字段
                        yield format_sse_event("checkpoint", {'session_id': new_session_id})
                    else:
                        # 继续现有对话
                        input_data = {
                            "messages": [HumanMessage(content=message)],
                            "title_generated": True,
                            "title": None
                        }
                
                # 处理从中断中恢复的情况
                if resume == 1 and resume_action:
                    # 使用Command类从中断中恢复
                    events = agent.astream_events(
                        Command(resume={"action": resume_action}),
                        version="v2",
                        config=config
                    )
                else:
                    # 正常操作或从检查点重新生成
                    events = agent.astream_events(
                        input_data,  # 如果从检查点恢复，input_data为None
                        version="v2",
                        config=config
                    )

                # 添加停止状态标志
                is_stopped = False
                try:
                    async for event in events:
                        # 在每次事件处理前检查停止状态
                        if thread_id in stop_events and stop_events[thread_id].is_set():
                            logger.info(f"检测到停止事件，中断流式处理，thread_id: {thread_id}")
                            is_stopped = True
                            break

                        event_type = event["event"]
                        if event_type == "on_chat_model_stream":
                            content = serialise_ai_message_chunk(event["data"]["chunk"])
                            if content and "wangwei" in event["tags"]:
                                # 使用message事件类型
                                yield format_sse_event("message", {'content': content})
                        elif event_type == "on_chat_model_end":
                            # 检查是否有工具调用
                            if hasattr(event["data"]["output"], "tool_calls") and event["data"]["output"].tool_calls:
                                for tool_call in event["data"]["output"].tool_calls:
                                    # 提取工具调用信息
                                    tool_name = tool_call["name"]
                                    tool_args = tool_call["args"]
                                    # 发送工具调用开始事件
                                    if tool_name != "IntentModel":
                                        yield format_sse_event("tool_start", {'name': tool_name, 'args': tool_args})
                        elif event_type == "on_tool_end":
                            output = event["data"]["output"]
                            # 使用tool_result事件类型
                            yield format_sse_event("tool_result", {'content': output.content, 'artifact': output.artifact, 'name': output.name})
                        elif event_type == "on_chain_stream":
                            # 检查是否包含中断信息
                            chunk = event["data"].get("chunk", {})
                            # 确保chunk是字典类型且包含__interrupt__键
                            if isinstance(chunk, dict) and "__interrupt__" in chunk and chunk["__interrupt__"]:
                                try:
                                    # 获取中断数据
                                    interrupt_data = chunk["__interrupt__"][0].value
                                    # 使用interrupt事件类型
                                    yield format_sse_event("interrupt", {'question': interrupt_data.get('question', ''), 'tool_call': interrupt_data.get('tool_call', {})})
                                except (IndexError, AttributeError) as e:
                                    logger.error(f"处理中断数据时出错: {e}", exc_info=True)

                        elif event_type == "on_chain_end" and isinstance(event["data"]["output"], dict) and event["data"]["output"].get("title") != None:
                            title = event["data"]["output"].get("title")
                except Exception as e:
                    logger.error(f"流式处理事件时出错: {e}", exc_info=True)
                    # 使用error事件类型
                    yield format_sse_event("error", {'content': f'处理响应时出错'})
                    
                if title != None:
                    # 使用title事件类型
                    yield format_sse_event("title", {'content': title})
                if is_stopped:
                    yield format_sse_event("stop", {'content': "用户已中断生成"})
                # 使用end事件类型
                yield format_sse_event("end", {})
        except Exception as e:
            logger.error(f"生成聊天响应时出错: {e}", exc_info=True)
            # 使用error事件类型
            yield format_sse_event("error", {'content': f'生成响应时出错'})
            # 使用end事件类型
            yield format_sse_event("end", {})
        finally:
            # 清理停止事件，防止内存泄漏
            if thread_id in stop_events:
                del stop_events[thread_id]
            if thread_id in stop_events_timestamps:
                del stop_events_timestamps[thread_id]
            logger.debug(f"已清理停止事件，thread_id: {thread_id}")
            
    async def get_chat_stream_response(self, message: str, user_id: int, brand_id: int, 
                                     session_id: Optional[str] = None, resume: int = 0, 
                                     resume_action: Optional[str] = None) -> StreamingResponse:
        """
        获取聊天流式响应
        
        Args:
            message: 用户消息
            user_id: 用户ID
            brand_id: 品牌ID
            session_id: 会话ID，如果为None则创建新会话
            resume: 是否从中断中恢复，1表示从中断中恢复，0表示正常操作
            resume_action: 恢复操作的动作，可以是"continue"或"reject"
        """
        if not message and resume == 0:
            return JSONResponse(
                content={"code": 10000, "success": False, "msg": "消息内容不能为空"}
            )
        
        return StreamingResponse(
            self.generate_chat_responses(message, user_id, brand_id, session_id, resume, resume_action),
            media_type="text/event-stream"
        )
    
    async def stop_generation(self, thread_id: str) -> Dict:
        """
        停止生成响应

        Args:
            thread_id: 会话ID/线程ID

        Returns:
            dict: 停止结果
        """
        try:
            # 确保停止事件存在，如果不存在则创建
            if thread_id not in stop_events:
                stop_events[thread_id] = asyncio.Event()
                stop_events_timestamps[thread_id] = time.time()
                logger.debug(f"为停止请求创建新的停止事件，thread_id: {thread_id}")

            # 设置停止事件
            stop_events[thread_id].set()
            logger.info(f"已设置停止事件，thread_id: {thread_id}")

            # 发布停止消息到Redis，确保在集群环境中所有节点都能收到停止请求
            try:
                message = json.dumps({"thread_id": thread_id})
                await RedisService.publish("stop_generation", message)
                logger.info(f"已发布停止消息到Redis，thread_id: {thread_id}")
            except Exception as e:
                logger.error(f"发布停止消息到Redis时出错: {e}", exc_info=True)
                # 即使Redis发布失败，本地停止事件已设置，仍然返回成功
                return {"code": 0, "success": True, "msg": "已停止生成（本地成功，Redis失败）"}

            return {"code": 0, "success": True, "msg": "已停止生成"}
        except Exception as e:
            logger.error(f"停止生成时出错: {e}", exc_info=True)
            return {"code": 10002, "success": False, "msg": f"停止生成时出错: {str(e)}"}
    
    async def regenerate_response(self, session_id: str, user_id: int, brand_id: int) -> StreamingResponse:
        """
        重新生成响应
        
        Args:
            session_id: 会话ID
            user_id: 用户ID
            brand_id: 品牌ID
        """
        try:
            async with get_aiomysql_saver() as checkpointer:
                agent = graph_builder.compile(checkpointer=checkpointer, name="wangwei_agent")
                
                config = {
                    "configurable": {
                        "thread_id": session_id
                    }
                }
                
                # 获取状态历史 - 正确处理异步生成器
                states = []
                async for state in agent.aget_state_history(config=config):
                    states.append(state)
                
                # 如果没有历史状态，返回错误
                if not states:
                    return JSONResponse(
                        content={"code": 10000, "success": False, "msg": "没有可用的检查点进行重新生成"}
                    )
                
                # 寻找next节点为"model"的状态
                model_state = None
                for state in states:
                    if hasattr(state, "next") and "intent_analysis" in state.next:
                        model_state = state
                        break
                
                # 如果没有找到合适的状态，返回错误
                if not model_state:
                    return JSONResponse(
                        content={"code": 10000, "success": False, "msg": "没有找到合适的检查点进行重新生成"}
                    )
                
                # 获取找到的状态的检查点ID
                checkpoint_id = model_state.config["configurable"]["checkpoint_id"]
                
                # 使用复用的generate_chat_responses函数，传入None作为message，表示从检查点恢复
                return StreamingResponse(
                    self.generate_chat_responses(None, user_id, brand_id, session_id, 0, None, checkpoint_id),
                    media_type="text/event-stream"
                )
                
        except Exception as e:
            logger.error(f"处理重新生成请求时出错: {e}", exc_info=True)
            
            # 如果已经返回了流式响应，使用SSE错误事件
            async def error_stream():
                # 使用error事件类型
                logger.error(f"处理重新生成请求时出错: {e}", exc_info=True)
                yield format_sse_event("error", {'content': "系统错误，请稍后再试"})
                yield format_sse_event("end", {})
                
            return StreamingResponse(
                error_stream(),
                media_type="text/event-stream"
            )
    
    async def get_chat_history(self, session_id: str) -> Dict:
        """
        获取聊天历史记录
        
        Args:
            session_id: 会话ID
        
        Returns:
            dict: 包含聊天历史记录和可能的中断状态
        """
        async with get_aiomysql_saver() as checkpointer:
            agent = graph_builder.compile(checkpointer=checkpointer, name="wangwei_agent")
            
            config = {
                "configurable": {
                    "thread_id": session_id
                }
            }
            
            # 获取会话状态
            try:
                state = await agent.aget_state(config=config)
                
                # 提取消息历史，确保human/ai交替形式
                messages = []
                current_ai_message = None
                tool_results = []
                
                # 处理state.values中的messages列表
                state_messages = state.values.get("messages", []) if hasattr(state, "values") else state.get("messages", [])
                
                for msg in state_messages:
                    if msg.type == "human":
                        # 如果有待处理的AI消息，先添加到结果中
                        if current_ai_message:
                            messages.append(current_ai_message)
                            current_ai_message = None
                            tool_results = []
                        
                        # 添加人类消息
                        messages.append({
                            "role": "human",
                            "content": msg.content
                        })
                        
                    elif msg.type == "ai":
                        # 如果消息只包含工具调用而没有内容，则跳过不显示在UI中
                        has_tool_calls = hasattr(msg, "tool_calls") and msg.tool_calls
                        if not msg.content and has_tool_calls:
                            continue
                            
                        # 如果有待处理的AI消息，先添加到结果中
                        if current_ai_message:
                            messages.append(current_ai_message)
                        
                        # 创建新的AI消息
                        current_ai_message = {
                            "role": "ai",
                            "content": msg.content,
                            "tool_results": [] if tool_results else None
                        }
                        
                        # 如果有之前收集的工具结果，添加到AI消息中
                        if tool_results:
                            current_ai_message["tool_results"] = tool_results.copy()
                            tool_results = []
                        
                    elif msg.type == "tool":
                        # 收集工具调用结果
                        tool_result = {
                            "name": msg.name,
                            "content": msg.content
                        }
                        
                        # 如果有结构化数据，也添加到结果中
                        if hasattr(msg, "artifact") and msg.artifact:
                            tool_result["artifact"] = msg.artifact
                        
                        # 将工具结果添加到当前收集的列表中
                        tool_results.append(tool_result)
                        
                        # 如果已经有AI消息，直接将工具结果添加到该消息中
                        if current_ai_message:
                            if not current_ai_message.get("tool_results"):
                                current_ai_message["tool_results"] = []
                            current_ai_message["tool_results"].append(tool_result)
                
                # 处理最后一个可能的AI消息
                if current_ai_message:
                    messages.append(current_ai_message)
                
                # 检查是否有中断状态
                interrupt_info = None
                if hasattr(state, "interrupts") and state.interrupts:
                    # 获取第一个中断
                    interrupt = state.interrupts[0]
                    interrupt_info = {
                        "question": interrupt.value.get("question", ""),
                        "tool_call": interrupt.value.get("tool_call", {})
                    }
                
                response_data = {
                    "messages": messages,
                }
                
                # 如果有中断信息，添加到响应中
                if interrupt_info:
                    response_data["interrupt"] = interrupt_info
                
                return {
                    "code": 0,
                    "success": True,
                    "msg": "获取聊天历史成功",
                    "data": response_data
                }
            except Exception as e:
                logger.error(f"获取聊天历史失败: {str(e)}", exc_info=True)
                return {
                    "code": 10000,
                    "success": False,
                    "msg": f"获取聊天历史失败: {str(e)}"
                }
    
    async def delete_session(self, session_id: str) -> Dict:
        """
        删除会话
        
        Args:
            session_id: 会话ID
        
        Returns:
            dict: 删除结果
        """
        try:
            async with get_aiomysql_saver() as checkpointer:
                # 删除指定thread_id的所有检查点数据
                await checkpointer.adelete_thread(thread_id=session_id)
                
                return {
                    "code": 0,
                    "success": True,
                    "msg": "删除会话成功"
                }
        except Exception as e:
            logger.error(f"删除会话失败: {str(e)}", exc_info=True)
            return {
                "code": 10000,
                "success": False,
                "msg": f"删除会话失败: {str(e)}"
            } 